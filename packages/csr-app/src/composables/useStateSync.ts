/**
 * 状态同步组合式函数
 * 用于在微前端环境中同步关键状态
 */

import { onMounted, onUnmounted } from 'vue'
import {
  syncAuthState,
  syncUserState,
  isInMicroFrontend,
  sendMessageToParent,
} from '@/utils/iframeNavigation'
import { getDeploymentConfig } from '@/config/deployment'
import { useStoryStore } from '@/store/story'
import { storeToRefs } from 'pinia'

export function useStateSync() {
  const storyStore = useStoryStore()
  const { currentStory, currentActor } = storeToRefs(storyStore)

  /**
   * 同步当前认证状态到主应用
   */
  const syncCurrentAuthState = () => {
    if (!isInMicroFrontend()) return

    const authData = {
      token: localStorage.getItem('token'),
      refreshToken: localStorage.getItem('refreshToken'),
      userId: localStorage.getItem('userId'),
      sessionId: localStorage.getItem('sessionId'),
    }

    // 只有当有实际数据时才同步
    if (authData.token || authData.userId) {
      syncAuthState(authData)
      console.log('🔐 CSR应用: 已同步认证状态到主应用')
    }
  }

  /**
   * 同步当前用户状态到主应用
   */
  const syncCurrentUserState = () => {
    if (!isInMicroFrontend()) return

    const userData = {
      user: localStorage.getItem('userInfo')
        ? JSON.parse(localStorage.getItem('userInfo')!)
        : null,
      language: localStorage.getItem('language'),
      theme: localStorage.getItem('theme'),
      userPreferences: localStorage.getItem('userPreferences')
        ? JSON.parse(localStorage.getItem('userPreferences')!)
        : null,
    }

    // 只有当有实际数据时才同步
    if (userData.user || userData.language || userData.theme) {
      syncUserState(userData)
      console.log('👤 CSR应用: 已同步用户状态到主应用')
    }
  }

  /**
   * 监听localStorage变化并自动同步
   */
  const handleStorageChange = (event: StorageEvent) => {
    if (!isInMicroFrontend()) return

    const { key, newValue } = event

    // 认证相关的key变化时同步认证状态
    if (['token', 'refreshToken', 'userId', 'sessionId'].includes(key || '')) {
      console.log('🔄 检测到认证状态变化:', key, newValue)
      setTimeout(syncCurrentAuthState, 100)
    }

    // 用户相关的key变化时同步用户状态
    if (
      ['userInfo', 'language', 'theme', 'userPreferences'].includes(key || '')
    ) {
      console.log('🔄 检测到用户状态变化:', key, newValue)
      setTimeout(syncCurrentUserState, 100)
    }
  }

  /**
   * 同步当前 story 和 actor 状态到主应用
   */
  const syncCurrentStoryState = () => {
    if (!isInMicroFrontend()) return

    const storyData = {
      currentStory: currentStory.value,
      currentActor: currentActor.value,
    }

    // 只有当有实际数据时才同步
    if (storyData.currentStory || storyData.currentActor) {
      sendMessageToParent('SYNC_STORY_STATE', storyData)
      console.log('📚 CSR应用: 已同步故事状态到主应用')
    }
  }

  /**
   * 从路由参数恢复 story 和 actor 状态
   */
  const restoreStoryFromRoute = () => {
    if (!isInMicroFrontend()) return

    const currentPath = window.location.pathname
    const params = currentPath.split('/')

    if (params.length >= 4) {
      const chatType = params[1]
      let storyId = ''
      let actorId = ''

      if (chatType === 'chat3') {
        // chat3 的参数顺序是 /chat3/:characterId/:storyId
        actorId = params[2]
        storyId = params[3]
      } else {
        // 其他的参数顺序是 /chatX/:storyId/:characterId
        storyId = params[2]
        actorId = params[3]
      }

      // 向主应用请求故事和角色数据
      if (storyId && actorId) {
        sendMessageToParent('REQUEST_STORY_DATA', { storyId, actorId })
        console.log('📚 CSR应用: 向主应用请求故事数据', { storyId, actorId })
      }
    }
  }

  /**
   * 处理来自主应用的状态同步
   */
  const handleParentStateSync = (event: MessageEvent) => {
    // 确保消息来自主应用
    const config = getDeploymentConfig()

    console.log('[AUTH_SYNC] CSR应用: 收到消息', {
      origin: event.origin,
      expectedOrigin: config.mainAppUrl,
      type: event.data?.type,
      hasPayload: !!event.data?.payload,
    })

    if (event.origin !== config.mainAppUrl) {
      console.warn(
        '[AUTH_SYNC] CSR应用: 消息来源不匹配',
        event.origin,
        'vs',
        config.mainAppUrl,
      )
      return
    }

    const { type, payload } = event.data

    if (type === 'STATE_SYNC') {
      console.log('[AUTH_SYNC] CSR应用: 收到主应用状态同步', payload)

      // 检查是否有新的登录状态
      const currentToken = localStorage.getItem('token')
      const newToken = payload.auth?.token
      const isNewLogin = !currentToken && newToken

      console.log('[AUTH_SYNC] CSR应用: 状态同步详情', {
        currentToken: currentToken
          ? currentToken.substring(0, 10) + '...'
          : null,
        newToken: newToken ? newToken.substring(0, 10) + '...' : null,
        isNewLogin,
        hasAuthPayload: !!payload.auth,
        hasUserPayload: !!payload.user,
      })

      // 同步认证状态
      if (payload.auth) {
        const { token, refreshToken, userId, sessionId } = payload.auth
        console.log('[AUTH_SYNC] CSR应用: 同步认证状态', {
          hasToken: !!token,
          hasRefreshToken: !!refreshToken,
          hasUserId: !!userId,
        })
        if (token) localStorage.setItem('token', token)
        if (refreshToken) {
          localStorage.setItem('refreshToken', refreshToken)
          localStorage.setItem('refresh_token', refreshToken) // CSR应用期望的key
        }
        if (userId) {
          localStorage.setItem('userId', userId)
          localStorage.setItem('user_id', userId) // CSR应用期望的key
        }
        if (sessionId) localStorage.setItem('sessionId', sessionId)
      }

      // 同步用户状态
      if (payload.user) {
        const { user, language, theme, userPreferences } = payload.user
        console.log('[AUTH_SYNC] CSR应用: 同步用户状态', {
          hasUser: !!user,
          userName: user?.name,
        })
        if (user) localStorage.setItem('userInfo', JSON.stringify(user))
        if (language) localStorage.setItem('language', language)
        if (theme) localStorage.setItem('theme', theme)
        if (userPreferences)
          localStorage.setItem(
            'userPreferences',
            JSON.stringify(userPreferences),
          )
      }

      // 无论是否为新登录，都需要更新用户store状态
      console.log('[AUTH_SYNC] CSR应用: 更新用户store状态')
      const userStore = useUserStore()
      if (userStore) {
        // 更新认证状态
        if (payload.auth?.token) {
          userStore.token = payload.auth.token
          userStore.refreshToken = payload.auth.refreshToken || null
          userStore.isAuthenticated = true
        }

        // 更新userId（从localStorage获取，因为payload中可能没有）
        const userId =
          localStorage.getItem('user_id') || localStorage.getItem('userId')
        if (userId) {
          userStore.userId = userId
        }

        // 更新用户信息
        if (payload.user?.user) {
          userStore.userInfo = payload.user.user
          console.log('[AUTH_SYNC] CSR应用: 用户信息已更新', {
            name: payload.user.user.name,
            coins: payload.user.user.coins,
            uuid: payload.user.user.uuid,
          })
        }

        // 如果是新登录，重新获取用户信息以确保数据最新
        if (isNewLogin) {
          console.log('[AUTH_SYNC] CSR应用: 新登录，重新获取用户信息')
          userStore.getUserInfo()
        } else {
          console.log('[AUTH_SYNC] CSR应用: 已有登录状态，用户store已更新')
        }
      }

      console.log('[AUTH_SYNC] CSR应用: 状态同步完成')
    } else if (type === 'STORY_DATA_RESPONSE') {
      console.log('📨 CSR应用: 收到主应用故事数据响应', payload)

      if (payload.story) {
        storyStore.setCurrentStory(payload.story)
        console.log('📚 CSR应用: 已设置当前故事', payload.story)
      }
      if (payload.actor) {
        storyStore.setCurrentActor(payload.actor)
        console.log('👤 CSR应用: 已设置当前角色', payload.actor)
      }
    }
  }

  // 组件挂载时设置监听器
  onMounted(() => {
    if (typeof window === 'undefined') return

    // 监听localStorage变化
    window.addEventListener('storage', handleStorageChange)

    // 监听来自主应用的消息
    window.addEventListener('message', handleParentStateSync)

    // 初始同步当前状态
    setTimeout(() => {
      syncCurrentAuthState()
      syncCurrentUserState()
      syncCurrentStoryState()
      // 如果没有当前故事或角色，尝试从路由恢复
      if (!currentStory.value || !currentActor.value) {
        restoreStoryFromRoute()
      }
    }, 500)
  })

  // 组件卸载时清理监听器
  onUnmounted(() => {
    if (typeof window === 'undefined') return

    window.removeEventListener('storage', handleStorageChange)
    window.removeEventListener('message', handleParentStateSync)
  })

  return {
    syncCurrentAuthState,
    syncCurrentUserState,
    syncCurrentStoryState,
    restoreStoryFromRoute,
    isInMicroFrontend: isInMicroFrontend(),
  }
}
