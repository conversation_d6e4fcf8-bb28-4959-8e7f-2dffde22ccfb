<template>
  <div class="live-stream-container">
    <div
      class="stream-wrapper"
      :class="{
        'video-playing': isPlayingVideo,
        'content-ready': contentReady,
      }"
      @click="handleContainerClick"
    >
      <!-- 调试信息 -->
      <div
        style="
          position: fixed;
          top: 10px;
          left: 10px;
          background: rgba(0, 0, 0, 0.8);
          color: white;
          padding: 10px;
          z-index: 9999;
          font-size: 12px;
        "
      >
        钻石数量: {{ userStore.userInfo?.coins || 0 }}<br />
        用户名: {{ userStore.userInfo?.name || 'N/A' }}<br />
        认证状态: {{ userStore.isAuthenticated }}<br />
        用户ID: {{ userStore.userId || 'N/A' }}
      </div>
      <!-- 通用头部组件 -->
      <CommonHeader
        :character-name="characterName"
        :character-avatar="characterAvatar"
        :show-viewer-count="true"
        :viewer-count="viewerCount"
        :is-live="isLive"
        @back-click="handleBackClick"
      >
        <template #back-button>
          <slot name="back-button" />
        </template>
        <template #header-right>
          <slot name="header-right" />
        </template>
      </CommonHeader>

      <!-- 主要内容容器 -->
      <div class="content-container">
        <!-- 专用无缝视频背景 - 仅用于直播间视频背景 -->
        <LiveStreamVideoBackground
          v-if="useSeamlessVideo && backgroundVideo"
          ref="videoBackgroundRef"
          :video-url="backgroundVideo"
          :is-muted="props.isMuted"
          class="seamless-background-container"
          @video-loaded="handleVideoLoaded"
          @video-error="handleVideoError"
          @audio-prompt-needed="handleAudioPromptNeeded"
          @play-prompt-needed="handlePlayPromptNeeded"
          @user-interaction-required="handleUserInteractionRequired"
        />

        <!-- 增强背景组件 - 包含视频播放器和其他背景类型 -->
        <EnhancedBackground
          v-else
          :background-video="backgroundVideo"
          :background-image="backgroundImage"
          :default-image="defaultImage"
          :animated-images="animatedImages"
          :transition-mode="'fade'"
          class="background-container"
          @resource-loading="handleResourceLoading"
          @transition-complete="handleTransitionComplete"
          @animated-image-complete="handleAnimatedImageComplete"
          @unlock-blur="handleUnlockBlur"
        />
      </div>

      <!-- 实时评论流插槽 -->
      <div class="comment-flow-container">
        <slot name="comment-flow" />
      </div>

      <!-- 心形粒子效果插槽 -->
      <div class="heart-particles-container">
        <slot name="heart-particles" />
      </div>

      <!-- 礼物动画效果 -->
      <div class="gift-animation-container">
        <LiveGiftAnimation ref="giftAnimationRef" />
      </div>

      <!-- 金币显示插槽 -->
      <div class="coin-display-container">
        <slot name="coin-display" />
      </div>

      <!-- 计时器插槽 -->
      <div class="timer-container">
        <slot name="timer" />
      </div>

      <!-- Stage 好感度系统插槽 -->
      <div class="stage-container">
        <slot name="stage" />
      </div>

      <!-- 底部控制栏插槽 -->
      <div class="stream-controls-container">
        <slot name="stream-controls" />
      </div>

      <!-- 覆盖层插槽 -->
      <div class="overlay-container" v-if="$slots.overlay">
        <slot name="overlay" />
      </div>

      <!-- 结束内容插槽 -->
      <div class="ending-container" v-if="$slots.ending">
        <slot name="ending" />
      </div>

      <!-- 聊天按钮插槽 -->
      <div class="chat-button-container" v-if="$slots['chat-button']">
        <slot name="chat-button" />
      </div>
    </div>

    <!-- 视频自动播放引导 -->
    <VideoAutoplayGuide
      v-if="showAutoplayGuide"
      :type="autoplayGuideType"
      @user-interaction="handleAutoplayGuideInteraction"
      @hide="hideAutoplayGuide"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import CommonHeader from '../CommonHeader/index.vue'
import EnhancedBackground from '@/mobile/views/chat2/components/ChatBackground/EnhancedBackground.vue'
import LiveStreamVideoBackground from './LiveStreamVideoBackground.vue'
import LiveGiftAnimation from '../LiveGiftAnimation/index.vue'
import VideoAutoplayGuide from '@/mobile/components/VideoAutoplayGuide.vue'
import type { Present } from '@/api/chat-multivariate'
import { useChat4Store } from '@/store/chat4'
import { useUserStore } from '@/store/user'

// 使用API中定义的Present类型
type Gift = Present

interface Props {
  isPlayingVideo: boolean
  backgroundVideo: string | null
  backgroundImage: string | null
  defaultImage: string | null
  animatedImages: string[]
  contentReady: boolean
  characterName?: string
  characterAvatar?: string
  viewerCount?: number
  isLive?: boolean
  useSeamlessVideo?: boolean
  isMuted?: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'container-click': [event: MouseEvent]
  'back-click': []
  'resource-loading': [loading: boolean]
  'transition-complete': []
  'animated-image-complete': []
}>()

// Store引用
const chat4Store = useChat4Store()
const userStore = useUserStore()

// LiveStreamContainer 不再直接处理 TTS 管理，所有逻辑已移至主页面

// TTS 管理器的消息处理已移至主页面

// 礼物动画组件引用
const giftAnimationRef = ref<InstanceType<typeof LiveGiftAnimation> | null>(
  null,
)

// 自动播放引导状态
const showAutoplayGuide = ref(false)
const autoplayGuideType = ref<'play' | 'audio' | 'interaction'>('play')
const videoBackgroundRef = ref<InstanceType<typeof LiveStreamVideoBackground>>()

// 播放礼物动画
const playGiftAnimation = (
  gift: Gift,
  senderName: string = 'You',
  quantity: number = 1,
) => {
  if (giftAnimationRef.value) {
    giftAnimationRef.value.playGiftAnimation(gift, senderName, quantity)
  }
}

// 方法
const handleContainerClick = (event: MouseEvent) => {
  emit('container-click', event)
}

const handleBackClick = () => {
  emit('back-click')
}

const handleResourceLoading = (loading: boolean) => {
  emit('resource-loading', loading)
}

const handleTransitionComplete = () => {
  emit('transition-complete')
}

const handleAnimatedImageComplete = () => {
  emit('animated-image-complete')
}

// 处理无缝视频组件的视频加载
const handleVideoLoaded = () => {
  emit('transition-complete')
}

// 处理无缝视频组件的视频错误
const handleVideoError = (error: string) => {
  console.error('无缝视频错误:', error)
  emit('resource-loading', false)
}

// 解锁模糊处理
const handleUnlockBlur = (tag: string, requiredHeartValue: number) => {
  console.log('Unlock blur requested in LiveStreamContainer:', {
    tag,
    requiredHeartValue,
  })
  chat4Store.requestUnlockBlur(tag, requiredHeartValue)
}

// 自动播放引导处理方法
const handleAudioPromptNeeded = () => {
  autoplayGuideType.value = 'audio'
  showAutoplayGuide.value = true
}

const handlePlayPromptNeeded = () => {
  autoplayGuideType.value = 'play'
  showAutoplayGuide.value = true
}

const handleUserInteractionRequired = () => {
  autoplayGuideType.value = 'interaction'
  showAutoplayGuide.value = true
}

const handleAutoplayGuideInteraction = () => {
  if (videoBackgroundRef.value) {
    if (autoplayGuideType.value === 'audio') {
      // 开启声音
      videoBackgroundRef.value.enableAudio()
    } else {
      // 处理用户交互
      videoBackgroundRef.value.handleUserInteraction()
    }
  }
  hideAutoplayGuide()
}

const hideAutoplayGuide = () => {
  showAutoplayGuide.value = false
}

// 暴露方法给父组件
defineExpose({
  playGiftAnimation,
  hideAutoplayGuide,
  showAutoplayGuide: (type: 'play' | 'audio' | 'interaction') => {
    autoplayGuideType.value = type
    showAutoplayGuide.value = true
  },
})
</script>

<style lang="less" scoped>
.live-stream-container {
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
  position: relative;
  overflow: hidden;
}

.stream-wrapper {
  position: relative;
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
  overflow: hidden;
  background: linear-gradient(180deg, #1f0038 0%, #000 100%);
  transition: opacity 0.3s ease;

  &.video-playing {
    background: transparent;
  }

  &.content-ready {
    opacity: 1;
  }
}

/* 通用头部组件已处理头部区域样式 */

.content-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.background-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.seamless-background-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  /* 强制禁用任何继承的过渡效果 */
  transition: none !important;
  animation: none !important;
  transform: none !important;
  opacity: 1 !important;
  visibility: visible !important;
}

.comment-flow-container {
  position: absolute;
  left: 10px;
  bottom: 74px;
  width: calc(100% - 140px); /* 留出右侧空间给心形粒子 */
  max-width: 406px;
  z-index: 10;
}

.heart-particles-container {
  position: absolute;
  right: 0px; /* 调整到右边缘，更好地覆盖heart-button */
  bottom: 0px; /* 调整到底部边缘，更好地覆盖heart-button */
  width: 100px; /* 减小宽度，更精确地定位 */
  height: 400px; /* 减小高度，但保持足够的空间让粒子飞行 */
  z-index: 15; /* 提高z-index确保显示在前面 */
  pointer-events: none;
}

.gift-animation-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 20; /* 确保礼物动画在最前面 */
  pointer-events: none;
}

.coin-display-container {
  position: absolute;
  top: 31px;
  right: 10px;
  z-index: 100;
}

.stage-container {
  position: absolute;
  top: 71px;
  right: 10px;
  z-index: 100;
}

.timer-container {
  position: absolute;
  top: 210px;
  right: 25px;
  z-index: 100;
}

.stream-controls-container {
  position: absolute;
  bottom: 10px;
  left: 10px;
  right: 10px;
  z-index: 100;
}

.overlay-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 200;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  pointer-events: none;
}

.ending-container {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
}

.chat-button-container {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 150;
}
</style>
