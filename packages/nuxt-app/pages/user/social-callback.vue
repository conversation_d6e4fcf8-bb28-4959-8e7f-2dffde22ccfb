<template>
  <div class="social-callback">
    <div class="loading-container">
      <div class="loading-spinner">
        <Icon name="lucide:loader-2" size="48" class="animate-spin" />
      </div>
      <h2 class="loading-title">Processing Login...</h2>
      <p class="loading-text">Please wait while we complete your login</p>
    </div>
  </div>
</template>

<script setup lang="ts">
// 页面元数据
definePageMeta({
  layout: false,
  ssr: false, // 客户端渲染，因为需要处理URL参数和localStorage
})

// SEO设置
useHead({
  title: 'Login Processing - PlayShot',
  meta: [{ name: 'robots', content: 'noindex, nofollow' }],
})

type SocialLoginType = 'google' | 'discord' | 'facebook'

interface SocialLoginExchangeResponse {
  auth: {
    access_token: string
    refresh_token: string
  }
  user: {
    uuid: string
    name: string
    email: string
    avatar_url: string
    status: string
    plan: string
    coins: number
    role: string
    gender: string
    create_time: string
  }
  is_first_login: boolean
}

const userStore = useUserStore()
const config = useRuntimeConfig()

// 同步用户状态到其他应用
const syncUserStateToApps = async () => {
  if (!import.meta.client) return

  try {
    // 同步认证状态
    const authData = {
      token: userStore.token,
      refreshToken: userStore.refreshToken,
      userId: userStore.userId,
      isAuthenticated: userStore.isAuthenticated,
      isGuest: userStore.isGuest,
    }

    // 同步用户信息
    const userData = {
      user: userStore.userInfo,
      language: 'en', // 可以从用户设置中获取
      theme: 'dark', // 可以从用户设置中获取
    }

    // 如果在iframe环境中，通知父应用
    if (window.parent !== window) {
      window.parent.postMessage(
        {
          type: 'AUTH_STATE_SYNC',
          data: authData,
        },
        '*',
      )

      window.parent.postMessage(
        {
          type: 'USER_STATE_SYNC',
          data: userData,
        },
        '*',
      )
    }

    console.log('✅ 用户状态已同步到其他应用')
  } catch (error) {
    console.error('❌ 用户状态同步失败:', error)
  }
}

// 处理社交登录回调
onMounted(async () => {
  try {
    // 获取URL参数
    const urlParams = new URLSearchParams(window.location.search)
    const code = urlParams.get('code')
    const error = urlParams.get('error')
    const login_type = urlParams.get('login_type') as SocialLoginType
    const redirect =
      urlParams.get('redirect') || sessionStorage.getItem('login_redirect')

    // 处理登录取消或错误
    if (error || !code || !login_type) {
      console.error('Social login error or cancelled:', {
        error,
        code,
        login_type,
      })
      await navigateTo('/user/login')
      return
    }

    // 调用API交换code获取用户数据
    const response = await $fetch<{
      code: string
      message?: string
      data: SocialLoginExchangeResponse
    }>(`${config.public.apiBase}/api/v1/social-login.exchange`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: {
        login_type,
        code,
        user_id: userStore.userInfo?.uuid || null,
      },
    })

    if (response.code !== '0' || !response.data) {
      throw new Error(
        response.message || 'Failed to exchange social login code',
      )
    }

    // 处理登录成功
    const { auth, user, is_first_login } = response.data

    // 更新用户store
    userStore.setToken(auth.access_token, auth.refresh_token)
    userStore.setUserInfo(user)

    // 同步到TokenManager
    if (import.meta.client) {
      const { setTokens, setUserInfo } = useTokenManager()
      setTokens(auth.access_token, auth.refresh_token)
      setUserInfo(user, user.role === 'guest' ? 'guest' : 'user')
    }

    // 标记需要同步状态到CSR应用（因为此时可能还没有iframe）
    if (import.meta.client) {
      localStorage.setItem('needSyncToCSR', 'true')
      localStorage.setItem('lastLoginTime', Date.now().toString())
      console.log('[AUTH_SYNC] social-callback: 设置同步标记', {
        needSyncToCSR: 'true',
        lastLoginTime: Date.now(),
        token: auth.access_token?.substring(0, 10) + '...',
        userId: user.uuid,
      })
    }

    // 同步用户状态到其他应用
    console.log('[AUTH_SYNC] social-callback: 开始同步用户状态到其他应用')
    await syncUserStateToApps()
    console.log('[AUTH_SYNC] social-callback: 同步用户状态完成')

    // 清除存储的重定向路径
    if (import.meta.client) {
      sessionStorage.removeItem('login_redirect')
    }

    // 处理重定向URL，只保留路径部分
    let redirectPath = '/'
    if (redirect) {
      try {
        const url = new URL(decodeURIComponent(redirect))
        redirectPath = url.pathname + url.search + url.hash
        console.log('Redirecting to:', redirectPath)
      } catch {
        // 如果解析URL失败，说明可能是相对路径，直接使用
        redirectPath = decodeURIComponent(redirect)
      }
    }

    // 显示成功消息
    console.log('Social login successful:', {
      user: user.name,
      type: login_type,
      isFirstLogin: is_first_login,
    })

    // 如果重定向到chat页面，延迟一下确保状态同步完成
    if (redirectPath.includes('/chat')) {
      console.log('🔄 重定向到chat页面，延迟确保状态同步完成')
      await new Promise((resolve) => setTimeout(resolve, 500))
    }

    // 重定向到指定页面或默认页面
    await navigateTo(redirectPath)
  } catch (error: any) {
    console.error('Social login callback error:', error)

    // 显示错误并重定向到登录页面
    await navigateTo('/user/login')
  }
})
</script>

<style lang="less" scoped>
.social-callback {
  min-height: calc(var(--vh, 1vh) * 100);
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(
    135deg,
    var(--bg-primary) 0%,
    var(--bg-secondary) 100%
  );
  padding: 24px;

  .loading-container {
    text-align: center;
    padding: 48px 32px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 24px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    max-width: 400px;
    width: 100%;

    .loading-spinner {
      margin-bottom: 24px;
      color: var(--primary-color);
    }

    .loading-title {
      font-size: 24px;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 12px;
    }

    .loading-text {
      font-size: 16px;
      color: var(--text-secondary);
      opacity: 0.8;
    }
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .social-callback {
    padding: 16px;

    .loading-container {
      padding: 32px 24px;
      border-radius: 16px;

      .loading-title {
        font-size: 20px;
      }

      .loading-text {
        font-size: 14px;
      }
    }
  }
}
</style>
